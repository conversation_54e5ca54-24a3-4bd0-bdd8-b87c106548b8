using AIServices.Models.Chat;
using AIServices.Utils;
using DevExpress.Pdf;
using DevExpress.Pdf.Drawing.Extensions;
using DevExpress.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;

using System.Windows.Forms;

namespace HIH.CRM.Import.AIOCR
{
    public partial class frmJKTD : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        private readonly TaskSequenceHelper task;
        private string currentPdfPath; // 当前PDF显示的数据路径
        private string dlxx; // 代理信息
        private string fileId; // 上传文件ID
        private DataTable dt; // 列表数据

        // 定义列表默认数据
        private readonly string[] fieldNames = { "分单号", "总单号", "船名航次", "起运港", "目的港", "件数", "体积", "毛重", "离港日期" };


        private bool showHighlight = false;
        private Rectangle viewerRect;    // 控件坐标（单位是像素
        private Timer highlightTimer;
        private RectangleF? documentRect; // 文档坐标（单位是 point）
        private int? documentPageNumber;


        public frmJKTD()
        {
            InitializeComponent();
            isLoadPerm = false;
            task = new TaskSequenceHelper("414242d05ce04f8f87b811dc7f42aa1c");

            // 初始化 Timer
            highlightTimer = new Timer();
            highlightTimer.Interval = 10000; // 显示1秒
            highlightTimer.Tick += HighlightTimer_Tick;

            // 绑定 PdfViewer 的 Paint 事件
            pdfView.Paint += PdfViewer1_Paint;

            InitializeGridData(); // 初始化GridView数据
        }

        private void InitializeGridData()
        {
            dt = new DataTable();
            dt.Columns.Add("key", typeof(string));
            dt.Columns.Add("value", typeof(string));

            // 填写列表数据
            foreach (string fieldName in fieldNames)
            {
                DataRow row = dt.NewRow();
                row["key"] = fieldName;
                row["value"] = ""; // 初始值为空
                dt.Rows.Add(row);
            }
            //绑定
            gd.DataSource = dt;
            gdv.BestFitColumns();
        }

        private void btnSelectFile_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "PDF文件|*.pdf",
                Title = "选择PDF文件"
            };
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                fileId = "";
                //在PDFview打开
                currentPdfPath = openFileDialog.FileName;
                pdfView.LoadDocument(openFileDialog.FileName);
                foreach (DataRow row in dt.Rows)
                {
                    row["value"] = ""; // 重置列表数据
                }
            }

        }

        private async void btnStartOCR_ItemClickAsync(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {


            if (string.IsNullOrEmpty(currentPdfPath))
            {
                ICF.ISD.ShowError("请先选择PDF文件！");
                return;
            }
            try
            {
                using (WaitDialogForm waitDialog = new WaitDialogForm("正在提取准备中...",
                                                    "请稍候",
                                                    new Size(500, 100), this))
                {


                    waitDialog.SetCaption("正在解析文件...");


                    foreach (DataRow row in dt.Rows)
                    {
                        row["value"] = ""; // 重置为默认空值
                    }
                    if (string.IsNullOrEmpty(fileId))
                    {
                        using (FileStream fileStream = File.OpenRead(currentPdfPath))
                        {
                            string fileName = Path.GetFileName(currentPdfPath);
                            fileId = await task.UploadFileAsync(fileStream, fileName);
                            //已经获取到fileID,将获取到的fileId，查询状态
                            string status = await task.PollFileStatusWithTimeoutAsync(fileId, 180);
                            if (status != "FILE_IS_READY")
                            {
                                ICF.ISD.ShowError("文件解析失败，状态：" + status + "！");
                                return;
                            }
                        }
                    }
                    waitDialog.SetCaption("解析文件完成，正在获取提取内容...");
                    //请求AI接口
                    //string message = "{\"version\":\"1.1\",\"description\":\"不同的提单，提单组成信息位置也不一样,请了解当前材料的提单,结构化文档解析规则,不要虚拟伪造数据返回\",\"fields\":{\"分单号\":{\"description\":\"分单号，可能带FRA前缀，不会是HHJK前缀\",\"extraction_steps\":[{\"step\":1,\"action\":\"优先匹配关键词\",\"target\":[\"SEA WAYBILL No\",\"HAWB No\",\"B/L NO\"],\"cleanup\":{\"remove\":[\" \",\"-\"],\"keep_prefix\":\"FRA\"}},{\"step\":2,\"action\":\"匹配FRA及后续内容\",\"pattern\":\"FRA[\\w\\-\\/\\s]+\",\"on_match\":\"full_value\"},{\"step\":3,\"action\":\"匹配无FRA的普通分单号\",\"pattern\":\"[A-Z0-9\\-]{5,}\",\"on_match\":\"full_value\"}],\"output_rules\":{\"start\":\"如果未找到分单号信息，请理解文档来选择正确的单号提取。\",\"empty_value\":\"\"}},\"总单号\":{\"description\":\"不可能是HHJK前缀，有一定概率是和分单号同行，也能填写联系电话或者传真号码。\",\"source_priority\":[\"MAWB\",\"MAWB#\",\"MASTER B/L\",\"MBL\",\"MASTER\"],\"extraction_logic\":{\"positional_rules\":[{\"condition\":\"分单号_exists_and_starts_with_FRA\",\"extract_right_of\":\"分单号\"}]},\"processing\":{\"remove_chars\":[\"-\",\" \"],\"to_uppercase\":true},\"empty_value\":\"\"},\"船名航次\":{\"description\": \"如果有\"DHL\",本身就可以作为承运人名称（Carrier Name）使用\",\"source_priority\":[\"VESSEL AND VOYAGE NO\",\"Requested Flight\",\"DHL\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"remove_suffixes\":[\"日期\",\"ETA\",\"ETD\",\"\\d{4}-\\d{2}-\\d{2}\"]},\"output_rules\":{\"start\":\"提取船名或者航次,不需要带航空日期后缀,保留空格部分\",\"empty_value\":\"\"}},\"起运港\":{\"source_priority\":[\"PLACE AND DATE OF ISSUE\",\"Airport of Departure\",\"ALBERT-SCHWEITZER-STRASSE\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"split_by\":[\",\",\"/\"],\"take_part\":0,\"remove_words\":[\"PORT\",\"AIRPORT\",\"APT\"]},\"output_rules\":{\"start\":\"如果提取到是城市，查看文档内容是否有机场代码，返回机场代码。\",\"empty_value\":\"\"}},\"目的港\":{\"source_priority\":[\"PORT OF DISCHARGE\",\"TO\",\"DESTINATION CODE\",\"Airport of Destination\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"split_by\":[\",\",\"/\"],\"take_part\":0,\"remove_words\":[\"PORT\",\"AIRPORT\",\"APT\",\"TO\"]},\"output_rules\":{\"start\":\"如果提取到是城市，查看文档内容是否有机场代码，返回机场代码。\",\"empty_value\":\"\"}},\"件数\":{\"source_priority\":[\"Total Items\",\"Pallet(s)\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":0},\"体积\":{\"source_priority\":[\"体积字段\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":\"\"},\"毛重\":{\"source_priority\":[\"Gross Cargo Weight\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":\"\"},\"离港日期\":{\"description\": \"离港日期、也叫 Arrive Date\",\"source_priority\":[\"SHIPPED ON BOARD DATE\",\"Arrive Date\"],\"processing\":{\"regex\":\"[\\d\\-/\\.]+\",\"date_formats\":[\"dd/mm/yyyy\",\"mm-dd-yyyy\",\"yyyy-mm-dd\",\"yyyy/MM/dd\"],\"output_format\":\"yyyy-MM-dd\"},\"empty_value\":\"\"}},\"global_rules\":{\"numeric_default\":0,\"string_default\":\"\",\"date_default\":\"\",\"strict_mode\":false}}";

                    string message = "{\"version\":\"1.1\",\"description\":\"不同的提单，提单组成信息位置也不一样,请了解当前材料的提单,结构化文档解析规则,不要虚拟伪造数据返回\",\"fields\":{\"代理名称\": {\"description\": \"这是提单的代理名称\",\"source_priority\": [\"DHL\",\"ROHLIG\",\"WAYBILL\",\"MEDITERRANEAN\",\"FIGHT\",\"KELSTERBACH\",\"FairCon\",\"FMS\",\"YANG MING\",\"SONICA\",\"HMM\",\"MAERSK\",\"NVOCC\",\"COPYNON\",\"NEW GLOBE\",\"江苏远洋太海\",\"CN LOGISTICS\",\"上海裕辉\",\"DOUBLE NINTH\",\"Consignees Account\"],\"empty_value\": \"\"},\"分单号\":{\"description\":\"分单号，可能带FRA前缀，不会是HHJK前缀\",\"extraction_steps\":[{\"step\":1,\"action\":\"优先匹配关键词\",\"target\":[\"SEA WAYBILL No\",\"HAWB No\",\"B/L NO\"],\"cleanup\":{\"remove\":[\" \",\"-\"],\"keep_prefix\":\"FRA\"}},{\"step\":2,\"action\":\"匹配FRA及后续内容\",\"pattern\":\"FRA[\\w\\-\\/\\s]+\",\"on_match\":\"full_value\"},{\"step\":3,\"action\":\"匹配无FRA的普通分单号\",\"pattern\":\"[A-Z0-9\\-]{5,}\",\"on_match\":\"full_value\"}],\"output_rules\":{\"start\":\"如果未找到分单号信息，请理解文档来选择正确的单号提取。\",\"empty_value\":\"\"}},\"总单号\":{\"description\":\"不可能是HHJK前缀，有一定概率是和分单号同行，也能填写联系电话或者传真号码。\",\"source_priority\":[\"MAWB\",\"MAWB#\",\"MASTER B/L\",\"MBL\",\"MASTER\"],\"extraction_logic\":{\"positional_rules\":[{\"condition\":\"分单号_exists_and_starts_with_FRA\",\"extract_right_of\":\"分单号\"}]},\"processing\":{\"remove_chars\":[\"-\",\" \"],\"to_uppercase\":true},\"empty_value\":\"\"},\"船名航次\":{\"description\": \"如果有\"DHL\",本身就可以作为承运人名称（Carrier Name）使用\",\"source_priority\":[\"VESSEL AND VOYAGE NO\",\"Requested Flight\",\"DHL\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"remove_suffixes\":[\"日期\",\"ETA\",\"ETD\",\"\\d{4}-\\d{2}-\\d{2}\"]},\"output_rules\":{\"start\":\"提取船名或者航次,不需要带航空日期后缀,保留空格部分\",\"empty_value\":\"\"}},\"起运港\":{\"source_priority\":[\"PLACE AND DATE OF ISSUE\",\"Airport of Departure\",\"ALBERT-SCHWEITZER-STRASSE\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"split_by\":[\",\",\"/\"],\"take_part\":0,\"remove_words\":[\"PORT\",\"AIRPORT\",\"APT\"]},\"output_rules\":{\"start\":\"如果提取到是城市，查看文档内容是否有机场代码，返回机场代码。\",\"empty_value\":\"\"}},\"目的港\":{\"source_priority\":[\"PORT OF DISCHARGE\",\"TO\",\"DESTINATION CODE\",\"Airport of Destination\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"split_by\":[\",\",\"/\"],\"take_part\":0,\"remove_words\":[\"PORT\",\"AIRPORT\",\"APT\",\"TO\"]},\"output_rules\":{\"start\":\"如果提取到是城市，查看文档内容是否有机场代码，返回机场代码。\",\"empty_value\":\"\"}},\"件数\":{\"source_priority\":[\"Total Items\",\"Pallet(s)\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":0},\"体积\":{\"source_priority\":[\"体积字段\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":\"\"},\"毛重\":{\"source_priority\":[\"Gross Cargo Weight\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":\"\"},\"离港日期\":{\"description\": \"离港日期、也叫 Arrive Date\",\"source_priority\":[\"SHIPPED ON BOARD DATE\",\"Arrive Date\"],\"processing\":{\"regex\":\"[\\d\\-/\\.]+\",\"date_formats\":[\"dd/mm/yyyy\",\"mm-dd-yyyy\",\"yyyy-mm-dd\",\"yyyy/MM/dd\"],\"output_format\":\"yyyy-MM-dd\"},\"empty_value\":\"\"}},\"global_rules\":{\"numeric_default\":0,\"string_default\":\"\",\"date_default\":\"\",\"strict_mode\":false}}";

                    ChatRequest chatRequest = new ChatRequest();
                    chatRequest.Question = message;
                    chatRequest.FileId = new string[] { fileId };
                    chatRequest.Temperature = 0.25;
                    //请求接口
                    string result = await task.ProcessSingleRequestsAsync(chatRequest);
                    BindDynamicToGridView(result);
                }
                ICF.ISD.ShowTips("提取完成，请检查列表数据，是否正确！");
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }


        /// <summary>
        /// 针对AI返回的数据进行处理
        /// </summary>
        /// <param name="strJson"></param>
        private void BindDynamicToGridView(string strJson)
        {
            try
            {
                strJson = strJson.Trim().TrimStart('{').TrimEnd('}');
                var fieldPairs = strJson.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                // 构建字典 key 、 value
                var dict = new Dictionary<string, string>();
                foreach (var pair in fieldPairs)
                {
                    var parts = pair.Split(new[] { ':' }, 2);
                    if (parts.Length == 2)
                    {
                        string key = parts[0].Trim().Trim('"');
                        string value = parts[1].Trim().Trim('"');

                        // 处理值中可能的转义字符
                        value = value.Replace("\\\"", "\"");

                        dict[key] = value;
                    }
                }

                // 更新value 列
                foreach (DataRow row in dt.Rows)
                {
                    string field = row["key"].ToString();
                    row["value"] = dict.ContainsKey(field) ? dict[field] : "";
                }
                dlxx = dict.ContainsKey("代理名称") ? dict["代理名称"] : "";
                //获取到代理名称
                //根据代理名称
                txtBox.Text = dlxx;
                // 绑定到 GridView
                gdv.RefreshData();
                gdv.BestFitColumns();
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError($"绑定数据到GridView时发生错误: {ex.Message}");
            }
        }

        private void gdv_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hInfo = gdv.CalcHitInfo(new Point(e.X, e.Y));
                if (e.Button == MouseButtons.Left && e.Clicks == 2)//判断是否左键双击
                {
                    //判断光标是否在行范围内
                    if (hInfo.InRow)
                    {
                        DataRow row = gdv.GetDataRow(hInfo.RowHandle);
                        string fieldName = row["key"].ToString();

                        // 从当前行获取 PDF 中的定位信息
                        int pageNumber = 1; // 页码，可以根据需要修改

                        bool isFlag = true;

                        // 这里可以根据不同字段设置不同的坐标和大小
                        float x = 0, y = 0, width = 0, height = 0;
                        switch (dlxx)
                        {
                            case var s when s.Contains("上海裕辉"):
                                switch (fieldName)
                                {
                                    case "分单号":
                                        x = 630;
                                        y = 510;
                                        width = 130;
                                        height = 50;
                                        break;
                                    case "总单号":
                                        x = 500;
                                        y = 510;
                                        width = 120;
                                        height = 50;
                                        break;
                                    case "船名航次":
                                        x = 300;
                                        y = 450;
                                        width = 100;
                                        height = 35;
                                        pageNumber = 2;
                                        break;
                                    case "起运港":
                                        x = 255;
                                        y = 465;
                                        width = 390;
                                        height = 35;
                                        pageNumber = 2;
                                        break;
                                    case "目的港":
                                        x = 255;
                                        y = 434;
                                        width = 190;
                                        height = 35;
                                        pageNumber = 2;
                                        break;
                                    case "件数":
                                        x = 770;
                                        y = 510;
                                        width = 50;
                                        height = 53;
                                        pageNumber = 1;
                                        break;
                                    case "毛重":
                                        x = 823;
                                        y = 510;
                                        width = 100;
                                        height = 53;
                                        pageNumber = 1;
                                        break;
                                    case "离港日期":
                                        x = 560;
                                        y = 15;
                                        width = 200;
                                        height = 53;
                                        pageNumber = 2;
                                        break;
                                    default:
                                        // 默认位置
                                        isFlag = false;
                                        break;
                                }
                                break;
                            case var s when s.Contains("MEDITERRANEAN"):
                                switch (fieldName)
                                {
                                    case "分单号":
                                        x = 655;
                                        y = 765;
                                        width = 350;
                                        height = 30;
                                        break;
                                    case "总单号":
                                        isFlag = false;
                                        break;
                                    case "船名航次":
                                        x = 220;
                                        y = 490;
                                        width = 200;
                                        height = 40;
                                        pageNumber = 1;
                                        break;
                                    case "起运港":
                                        x = 513;
                                        y = 490;
                                        width = 150;
                                        height = 40;
                                        pageNumber = 1;
                                        break;
                                    case "目的港":
                                        x = 512;
                                        y = 475;
                                        width = 190;
                                        height = 40;
                                        pageNumber = 1;
                                        break;
                                    case "件数":
                                        x = 400;
                                        y = 320;
                                        width = 350;
                                        height = 180;
                                        pageNumber = 1;
                                        break;
                                    case "毛重":
                                        x = 902;
                                        y = 340;
                                        width = 100;
                                        height = 53;
                                        pageNumber = 1;
                                        break;
                                    case "离港日期":
                                        x = 480;
                                        y = -60;
                                        width = 200;
                                        height = 53;
                                        pageNumber = 1;
                                        break;
                                    default:
                                        // 默认位置
                                        isFlag = false;
                                        break;
                                }
                                break;
                            case var s when s.Contains("ROHLIG"):
                                switch (fieldName)
                                {
                                    case "分单号":
                                        //x = 520;
                                        //y = 765;
                                        x = 2.5F;
                                        y = 1;
                                        width = 200;
                                        height = 40;
                                        break;
                                    case "总单号":
                                        isFlag = false;
                                        break;
                                    case "船名航次":
                                        x = -110;
                                        y = 485;
                                        width = 200;
                                        height = 45;
                                        pageNumber = 1;
                                        break;
                                    case "起运港":
                                        x = 648;
                                        y = 485;
                                        width = 250;
                                        height = 55;
                                        pageNumber = 1;
                                        break;
                                    case "目的港":
                                        x = 230;
                                        y = 470;
                                        width = 190;
                                        height = 40;
                                        pageNumber = 1;
                                        break;
                                    case "件数":
                                        x = 450;
                                        y = 440;
                                        width = 140;
                                        height = 80;
                                        pageNumber = 1;
                                        break;
                                    case "毛重":
                                        x = 965;
                                        y = 450;
                                        width = 80;
                                        height = 60;
                                        pageNumber = 1;
                                        break;
                                    case "离港日期":
                                        x = 230;
                                        y = 290;
                                        width = 210;
                                        height = 53;
                                        pageNumber = 1;
                                        break;
                                    default:
                                        // 默认位置
                                        isFlag = false;
                                        break;
                                }
                                break;

                            default:
                                isFlag = false;
                                break;
                        }
                        if (!isFlag)
                        {
                            return;
                        }
                        //switch (fieldName)
                        //{
                        //    case "分单号":
                        //        x = 630;
                        //        y = 510;
                        //        width = 130;
                        //        height = 50;
                        //        break;
                        //    case "总单号":
                        //        x = 500;
                        //        y = 510;
                        //        width = 120;
                        //        height = 50;
                        //        break;
                        //    case "船名航次":
                        //        x = 300;
                        //        y = 450;
                        //        width = 100;
                        //        height = 35;
                        //        pageNumber = 2;
                        //        break;
                        //    case "目的港":
                        //        x = 255;
                        //        y = 434;
                        //        width = 190;
                        //        height = 35;
                        //        pageNumber = 2;
                        //        break;
                        //    case "件数":
                        //        x = 770;
                        //        y = 510;
                        //        width = 50;
                        //        height = 53;
                        //        pageNumber = 1;
                        //        break;
                        //    case "毛重":
                        //        x = 823;
                        //        y = 510;
                        //        width = 100;
                        //        height = 53;
                        //        pageNumber = 1;
                        //        break;
                        //    case "离港日期":
                        //        x = 560;
                        //        y = 15;
                        //        width = 200;
                        //        height = 53;
                        //        pageNumber = 2;
                        //        break;
                        //    default:
                        //        // 默认位置
                        //        x = 770;
                        //        y = 510;
                        //        width = 50;
                        //        height = 53;
                        //        break;
                        //}

                       


                        RectangleF pdfRect = new RectangleF(x, y, width, height);
                        viewerRect = ConvertPdfRectToViewerRect(pageNumber, pdfRect);

                        showHighlight = true;
                        pdfView.Invalidate(); // 刷新绘图
                        highlightTimer.Start();
                    }
                }
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError(ex.Message);
            }
        }



        #region 绘制红框
        private void HighlightTimer_Tick(object sender, EventArgs e)
        {
            showHighlight = false;
            pdfView.Invalidate(); // 清除红框
            highlightTimer.Stop();
        }

        private void PdfViewer1_Paint(object sender, PaintEventArgs e)
        {
            if (showHighlight)
            {
                Point scrollPos = pdfView.AutoScrollPosition;
                using (Pen redPen = new Pen(Color.Red, 2))
                {
                    e.Graphics.DrawRectangle(redPen, viewerRect);
                }
            }
        }

        private Rectangle ConvertPdfRectToViewerRect(int pageNumber, RectangleF pdfRect)
        {

            // 1. 取当前页大小（单位 point）
            var pdfPageSize = pdfView.GetPageSize(pageNumber);

            // 2. 计算 Y 坐标翻转（PDF左下为原点，控件左上为原点）
            float flippedY = pdfPageSize.Height - pdfRect.Y;

            // 3. 取矩形左下角点（PDF坐标）
            var pdfPoint = new PdfPoint(pdfRect.X, flippedY);

            // 4. 构造定位对象
            var position = new PdfDocumentPosition(pageNumber, pdfPoint);

            // 5. 转换成控件坐标（像素）
            PointF clientPt = pdfView.GetClientPoint(position);

            // 6. 缩放比例（pdfView.ZoomFactor是百分比）
            float zoom = pdfView.ZoomFactor / 100f;

            // 7. 根据缩放计算显示的矩形大小（像素）
            int width = (int)Math.Round(pdfRect.Width * zoom);
            int height = (int)Math.Round(pdfRect.Height * zoom);

            // 8. 计算显示矩形左上角（控件坐标）
            int x = (int)Math.Round(clientPt.X);
            int y = (int)Math.Round(clientPt.Y);

            // 9. 滚动居中显示该点
            ScrollToCenter(clientPt);

            return new Rectangle(x, y, width, height);
        }

        private void ScrollToCenter(PointF clientPt)
        {
            Size viewSize = pdfView.ClientSize;

            int offsetX = (int)(clientPt.X - viewSize.Width / 2);
            int offsetY = (int)(clientPt.Y - viewSize.Height / 2);

            pdfView.ScrollHorizontal(offsetX);
            pdfView.ScrollVertical(offsetY);
        }
        private void SafeScrollToPosition(PdfDocumentPosition position)
        {
            try
            {

                // 转换成控件坐标（像素）
                PointF clientPt = pdfView.GetClientPoint(position);

                // 计算滚动偏移量，使目标点居中
                int offsetX =  (int)(clientPt.X - pdfView.ClientRectangle.Width / 2);
                int offsetY = (int)(clientPt.Y - pdfView.ClientRectangle.Height / 2);

                pdfView.ScrollHorizontal(offsetX);
                pdfView.ScrollVertical(offsetY);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ScrollToRegionCenter异常: {ex.Message}");
            }
        }



        private void ScrollToPosition(DevExpress.Pdf.PdfDocumentPosition position)
        {
            int x = (int)((pdfView.GetClientPoint(position).X / (pdfView.ZoomFactor * 0.01)) - (pdfView.ClientRectangle.Width / 2));
            int y = (int)((pdfView.GetClientPoint(position).Y / (pdfView.ZoomFactor * 0.01)) - (pdfView.ClientRectangle.Height / 2));
            pdfView.ScrollHorizontal(x);
            pdfView.ScrollVertical(y);
        }


        #endregion

        private float GetCurrentZoom()
        {
            // v20.2 的 PdfViewer 没有公开 Zoom 属性，可以尝试：
            // 1. 手动维护一个 zoom 变量
            // 2. 或者假设当前为 1.0
            return 1.0f;
        }

        private void pdfView_MouseMove(object sender, MouseEventArgs e)
        {
            try
            {

                int mouseX = e.Location.X;
                int mouseY = e.Location.Y;

                // 当前页
                int pageNumber = pdfView.CurrentPageNumber;
                var pageSize = pdfView.GetPageSize(pageNumber);

                // 当前缩放比例（1.0表示100%）
                float zoom = pdfView.ZoomFactor / 100f;

                // 转换 X 坐标，鼠标坐标除以缩放
                float pdfX = mouseX / zoom;

                // Y 轴翻转，控件 Y 轴是从上到下，PDF 是从下到上
                float pdfY = pageSize.Height - (mouseY / zoom);

                txtBox.Text = $"PDF 坐标: X={pdfX:F2}, Y={pdfY:F2}, 页码: {pageNumber}";

                //// 获取控件坐标
                //int controlX = e.Location.X;
                //int controlY = e.Location.Y;

                //// 估算 PDF 坐标（假设当前缩放比例为 1.0）
                //float pdfX = controlX / GetCurrentZoom();
                //float pdfY = controlY / GetCurrentZoom();

                //// 显示信息
                //txtBox.Text = $"Control: ({controlX}, {controlY}), PDF: ({pdfX:F0}, {pdfY:F0})";
            }
            catch
            {
                // 忽略无效位置
            }
        }

        private void pdfView_MouseClick(object sender, MouseEventArgs e)
        {
            //float pdfX = e.X / GetCurrentZoom();
            //float pdfY = e.Y / GetCurrentZoom();

            //string fieldName = PromptForFieldName(); // 弹出输入框让用户输入字段名

            //SaveFieldCoordinate(fieldName, 1, pdfX, pdfY, 200, 30); // 保存字段坐标


        }

        private void pdfView_ZoomChanged(object sender, DevExpress.XtraPdfViewer.PdfZoomChangedEventArgs e)
        {
            pdfView.Invalidate(); // 强制重绘
        }
    }
}
